# 阵地广告项目重构需求文档

## 项目概述

阵地广告项目（pad-resource）是一个多模块的Java项目，当前架构存在服务耦合问题，需要进行分布式服务重构。

## 当前架构问题

### 现状分析
- **admin-api模块**：为外部系统提供API接口，直接访问数据库
- **web模块**：为外部系统提供Web服务，直接访问数据库
- **物理服务层面**：缺乏分布式RPC服务架构，业务逻辑分散

### 存在问题
1. 服务耦合度高，直接数据库访问
2. 缺乏统一的业务服务层
3. 不利于服务的独立部署和扩展
4. 维护成本高，代码重复

## 重构目标

### 总体目标
将现有的直接数据库访问模式重构为分布式RPC服务架构，实现业务服务的统一管理。

### 具体目标
1. **服务分层**：建立清晰的服务分层架构
   - API层：admin-api、web模块
   - 服务层：admin-service（统一业务服务）
   - 数据层：数据库访问层

2. **RPC服务化**：将业务逻辑提炼到admin-service中，通过RPC调用提供服务

3. **接口标准化**：统一API接口规范和数据格式

## 重构计划

### 阶段一：admin-api模块重构（当前阶段）
**优先级**：高
**原因**：为了安全起见，先重构admin-api模块

#### 重构步骤
1. **验收测试准备**
   - 编写完整的API验收测试用例
   - 基于现有数据库数据创建测试数据
   - 确保测试覆盖所有API接口

2. **服务接口设计**
   - 设计admin-service的RPC接口
   - 定义数据传输对象（DTO）
   - 制定接口规范文档

3. **业务逻辑迁移**
   - 将admin-api中的业务逻辑迁移到admin-service
   - 保持原有业务逻辑不变
   - 确保数据一致性

4. **接口适配**
   - 修改admin-api调用方式，从直接数据库访问改为RPC调用
   - 保持对外API接口不变
   - 确保向后兼容性

### 阶段二：web模块重构（后续阶段）
**优先级**：中
**依赖**：admin-api重构完成

### 阶段三：其他模块优化（后续阶段）
**优先级**：低
**依赖**：前两个阶段完成

## 技术规范

### 数据库配置
- **连接地址**：*************************************************
- **用户名**：mxuser
- **密码**：mxuser123456

### 开发规范
- **作者注解**：所有新创建的类需要添加`<AUTHOR>
- **代码风格**：遵循项目现有代码风格
- **测试覆盖**：新增代码需要有对应的单元测试

### 质量保证
1. **测试驱动**：先写测试，后重构代码
2. **渐进式重构**：分模块、分阶段进行
3. **向后兼容**：确保重构过程中不影响现有功能
4. **文档同步**：及时更新相关文档

## 风险控制

### 技术风险
- **数据一致性**：重构过程中确保数据不丢失
- **接口兼容性**：保证对外接口的向后兼容
- **性能影响**：RPC调用可能带来的性能开销

### 缓解措施
1. **充分测试**：编写完整的验收测试用例
2. **灰度发布**：分阶段、小范围验证
3. **回滚方案**：准备快速回滚机制
4. **监控告警**：建立完善的监控体系

## 验收标准

### 功能验收
- [ ] 所有原有API功能正常
- [ ] 新的RPC服务正常运行
- [ ] 数据一致性验证通过
- [ ] 性能指标满足要求

### 代码质量
- [ ] 代码审查通过
- [ ] 单元测试覆盖率达标
- [ ] 集成测试通过
- [ ] 文档更新完整

## 项目时间线

### 第一阶段：准备工作（1-2周）
- 环境搭建和工具配置
- 验收测试用例编写
- 技术方案详细设计

### 第二阶段：admin-api重构（2-3周）
- 业务逻辑迁移
- RPC接口实现
- 测试验证

### 第三阶段：web模块重构（2-3周）
- 参考admin-api重构经验
- 业务逻辑迁移
- 测试验证

## 后续维护

### 监控指标
- API响应时间
- RPC调用成功率
- 数据库连接池状态
- 系统资源使用情况

### 优化方向
- 缓存策略优化
- 数据库查询优化
- 服务治理完善
- 监控告警优化

---

**文档版本**：v1.0  
**创建时间**：2025-08-28  
**创建人**：fu.wei  
**最后更新**：2025-08-28
