package cmc.pad.resource.admin.api.controller;

import cmc.pad.resource.application.query.AdvertisingPointLeasingPriceQueryService;
import cmc.pad.resource.application.query.CinemaLevelQueryService;
import cmc.pad.resource.application.query.data.AdvertisingPointPriceQuery;
import cmc.pad.resource.application.query.data.CinemaLevelInfo;
import cmc.pad.resource.domain.cinema.CinemaRepository;
import cmc.pad.resource.domain.dictionary.DictionaryDomainService;
import cmc.pad.resource.domain.price.AdvertisingPointLeasingPrice;
import cmc.pad.resource.admin.api.model.price.AdvertisingPointLeasingPriceModel;
import cmc.pad.resource.admin.api.util.MoneyUtils;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.data.PageResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

/**
 * 宣传点位报价
 *
 * <AUTHOR>
 * @Date 2019/3/29 17:10
 * @Version 1.0
 */
@Slf4j
@RestController
@RequestMapping("advertising-point-leasing/price")
public class AdvertisingPointLeasingController extends BaseLeasingController {

    private final AdvertisingPointLeasingPriceQueryService queryService;

    @Autowired
    AdvertisingPointLeasingController(AdvertisingPointLeasingPriceQueryService queryService,
                                      CinemaLevelQueryService cinemaLevelQueryService,
                                      CinemaRepository cinemaRepository,
                                      DictionaryDomainService dictionaryDomainService) {
        super(cinemaLevelQueryService, cinemaRepository, dictionaryDomainService);
        this.queryService = queryService;
    }


    @RequestMapping(value = "query", method = {GET})
    public List queryByGet(@ModelAttribute @Validated AdvertisingPointLeasingPriceModel.QueryParams params) {
        return getList(params);
    }

    @RequestMapping(value = "query", method = {POST})
    public List queryByPost(@RequestBody @Validated AdvertisingPointLeasingPriceModel.QueryParams params) {
        return getList(params);
    }

    private List getList(AdvertisingPointLeasingPriceModel.QueryParams params) {
        log.info(">>>查询宣传点位租赁刊例价, {}", params);
        String cinemaCode = params.getCinemaCode();
        String cityDistrictLevel = params.getCityLevel();
        String cinemaLevel = params.getCinemaLevel();
        if (StringUtils.isNotBlank(cinemaCode)) {
            CinemaLevelInfo cinemaLevelInfo = getCinemaLevel(cinemaCode);
            cinemaLevel = cinemaLevelInfo.getCinemaLevel();
            cityDistrictLevel = cinemaLevelInfo.getCityDistrictLevel();
        } else {
            validateCityDistrictLevel(cityDistrictLevel);
            validateCinemaLevel(cinemaLevel);
        }
        AdvertisingPointPriceQuery query = new AdvertisingPointPriceQuery();
        query.setCityLevel(cityDistrictLevel);
        query.setCinemaLevel(cinemaLevel);
        PageResult<AdvertisingPointLeasingPrice> pageResult = queryService.effectivePage(query);
        List<AdvertisingPointLeasingPrice> items = pageResult.getItems();
        int totalCount = pageResult.getTotalCount();
        if (totalCount > 0) {
            return items.stream().map(o -> {
                AdvertisingPointLeasingPriceModel.Info info = new AdvertisingPointLeasingPriceModel.Info();
                info.setCityLevel(o.getCityLevel());
                info.setCinemaLevel(o.getCinemaLevel());
                info.setBasePrice(MoneyUtils.centConvertYuan(o.getMinTotalPrice()));
                info.setBaseArea(o.getMinArea());
                info.setExtendedPrice(MoneyUtils.centConvertYuan(o.getExpandedUnitPrice()));
                return info;
            }).collect(Collectors.toList());
        }
        return Collections.EMPTY_LIST;
    }

}
