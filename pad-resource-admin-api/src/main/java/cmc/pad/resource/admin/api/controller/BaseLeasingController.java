package cmc.pad.resource.admin.api.controller;

import cmc.pad.resource.application.AppError;
import cmc.pad.resource.application.query.CinemaLevelQueryService;
import cmc.pad.resource.application.query.data.CinemaLevelInfo;
import cmc.pad.resource.domain.cinema.Cinema;
import cmc.pad.resource.domain.cinema.CinemaRepository;
import cmc.pad.resource.domain.dictionary.DictionaryDomainService;
import cmc.pad.resource.admin.api.constant.LeaseMethod;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static mtime.lark.db.jsd.Shortcut.f;

@Slf4j
abstract public class BaseLeasingController {
    private final CinemaLevelQueryService cinemaLevelQueryService;
    private final CinemaRepository cinemaRepository;
    private final DictionaryDomainService dictionaryDomainService;

    BaseLeasingController(CinemaLevelQueryService cinemaLevelQueryService, CinemaRepository cinemaRepository, DictionaryDomainService dictionaryDomainService) {
        this.cinemaLevelQueryService = cinemaLevelQueryService;
        this.cinemaRepository = cinemaRepository;
        this.dictionaryDomainService = dictionaryDomainService;
    }

    CinemaLevelInfo getCinemaLevel(String cinemaCode) {
        CinemaLevelInfo cinema = cinemaLevelQueryService.getCinema(cinemaCode);
        log.info(">>>影城内码:{}, 影城信息:{}", cinemaCode, cinema);
        if (Objects.isNull(cinema)) {
            throw AppError.CINEMA_LEVEL_NOT_CONFIG.toException();
        }
        String cinemaLevel = cinema.getCinemaLevel();
        if (StringUtils.isBlank(cinemaLevel)) {
            throw AppError.CINEMA_LEVEL_NOT_CONFIG.toException();
        }
        String cityDistrictLevel = cinema.getCityDistrictLevel();
        if (StringUtils.isBlank(cityDistrictLevel)) {
            throw AppError.CITY_DISTRICT_NO_LEVEL.toException();
        }
        return cinema;
    }

    void validateCinemaCode(String cinemaCode) {
        Optional<Cinema> one = cinemaRepository.findOne(f(Cinema.C_CODE, cinemaCode));
        if (!one.isPresent()) {
            throw AppError.CINEMA_NOT_EXIST.toException();
        }
    }

    void validateCinemaLevel(String cinemaLevel) {
        List<String> levelCodes = dictionaryDomainService.allCinemaLevelCodes();
        if (!levelCodes.contains(cinemaLevel)) {
            throw AppError.CINEMA_LEVEL_NOT_EXIST.toException();
        }
    }

    void validateCityDistrictLevel(String cityLevel) {
        List<String> codes = dictionaryDomainService.allCityLevelCodes();
        if (!codes.contains(cityLevel)) {
            throw AppError.CINEMA_LEVEL_NOT_EXIST.toException();
        }
    }

    void validateLeaseMethod(String leaseMethod) {
        LeaseMethod method = LeaseMethod.valueByCode(leaseMethod);
        if (Objects.isNull(method)) {
            throw AppError.LEASING_METHOD_NOT_EXIST.toException();
        }
    }

    void validateMovieHallType(String movieHallType) {
        List<String> codes = dictionaryDomainService.allMovieHallTypeCodes();
        if (!codes.contains(movieHallType)) {
            throw AppError.MOVIE_HALL_TYPE_NOT_EXIST.toException();
        }
    }
}

