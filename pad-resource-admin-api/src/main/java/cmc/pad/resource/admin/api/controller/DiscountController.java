package cmc.pad.resource.admin.api.controller;

import cmc.pad.resource.admin.api.model.CoefficientInfo;
import cmc.pad.resource.admin.api.model.QueryDiscountParams;
import cmc.pad.resource.domain.discount.*;
import com.google.common.base.Strings;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.db.jsd.Filter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static cmc.pad.resource.application.AppError.*;
import static mtime.lark.db.jsd.Shortcut.f;
import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

/**
 * 折扣controller
 */
@Slf4j
@RestController
@RequestMapping("discount")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class DiscountController {

    private final DiscountRepository discountRepository;
    private final DiscountRuleRepository discountRuleRepository;

    @RequestMapping(value = "coefficient/match", method = {POST})
    public List queryByPost(@RequestBody @Validated QueryDiscountParams queryDiscountParams) {
        return queryByGet(queryDiscountParams);
    }

    @RequestMapping(value = "coefficient/match", method = {GET})
    public List queryByGet(@ModelAttribute @Validated QueryDiscountParams queryDiscountParams) {
        log.info(">>>查询折扣系数, {}", queryDiscountParams);
        //为空不校验
        if (StringUtils.isBlank(queryDiscountParams.getBusinessType())) {
            throw DISCOUNT_BUSINESS_TYPE_NOT_NULL.toException();
        }
        String businessType = queryDiscountParams.getBusinessType();
        if (!Strings.isNullOrEmpty(businessType) && !businessType.equalsIgnoreCase("YX") && !businessType.equalsIgnoreCase("WZ")
                && !businessType.equalsIgnoreCase("GD") && !businessType.equalsIgnoreCase("XC")
                && !businessType.equalsIgnoreCase("YT") && !businessType.equalsIgnoreCase("GMT")) {
            throw DISCOUNT_METHOD_NOT_SUPPORT.toException();
        }
        List<CoefficientInfo> coefficientInfoList = new ArrayList<>();
        if (!Strings.isNullOrEmpty(queryDiscountParams.getDiscountMethod())) {
            int discountType;
            Float amount;
            if (DiscountType.DURATION.name().equalsIgnoreCase(queryDiscountParams.getDiscountMethod())) {
                discountType = 1;
                amount = queryDiscountParams.getDuration();
            } else if (DiscountType.AREA.name().equalsIgnoreCase(queryDiscountParams.getDiscountMethod())) {
                discountType = 2;
                amount = queryDiscountParams.getArea();
            } else {
                throw DISCOUNT_METHOD_NOT_SUPPORT.toException();
            }
            Float coefficient = matchCoefficient(queryDiscountParams.getBusinessType(), discountType, amount);
            if (coefficient == 0f) {
                coefficientInfoList.add(new CoefficientInfo(queryDiscountParams.getDiscountMethod(), 1f));
            } else {
                coefficientInfoList.add(new CoefficientInfo(queryDiscountParams.getDiscountMethod(), coefficient));
            }
            return coefficientInfoList;
        } else {
            //匹配两种折扣方式的折扣系数
            if (queryDiscountParams.getArea() == null && queryDiscountParams.getDuration() == null) {
                throw DISCOUNT_DURATION_AREA_NOT_NULL.toException();
            } else {
                if (queryDiscountParams.getDuration() != null) {
                    Float coefficient = matchCoefficient(queryDiscountParams.getBusinessType(), 1, queryDiscountParams.getDuration());
                    if (coefficient != 0f) {
                        coefficientInfoList.add(new CoefficientInfo(DiscountType.DURATION.name(), coefficient));
                    } else {
                        coefficientInfoList.add(new CoefficientInfo(DiscountType.DURATION.name(), 1f));
                    }
                }
                if (queryDiscountParams.getArea() != null) {//面积数不能为空
                    Float coefficient = matchCoefficient(queryDiscountParams.getBusinessType(), 2, queryDiscountParams.getArea());
                    if (coefficient != 0f) {
                        coefficientInfoList.add(new CoefficientInfo(DiscountType.AREA.name(), coefficient));
                    } else {
                        coefficientInfoList.add(new CoefficientInfo(DiscountType.AREA.name(), 1f));
                    }
                }
                if (coefficientInfoList.size() == 0) {
                    throw DISCOUNT_FACTOR_NOT_MATCHED.toException();
                } else {
                    return coefficientInfoList;
                }
            }
        }
    }

    private Float matchCoefficient(String businessType, int discountType, Float amount) {
        Filter filter = Filter.create("business_type", businessType);
        Optional<Discount> optional = discountRepository.findOne(filter.and(f("discount_type", discountType)));
        final Float[] factor = {0f};
        optional.ifPresent(discount -> {
            List<DiscountRule> rules = discountRuleRepository.findMany(Filter.create("discount_id", discount.getId()));
            BigDecimal compared = new BigDecimal(0);
            if (discountType == 2) {
                if (amount == null) {
                    throw DISCOUNT_AREA_NOT_NULL.toException();
                }
                compared = new BigDecimal(amount);
            } else if (discountType == 1) {
                if (amount == null) {
                    throw DISCOUNT_DURATION_NOT_NULL.toException();
                }
                compared = new BigDecimal(amount);
            }
            for (DiscountRule rule : rules) {
                if (rule.getComparisonSymbol() == 1) {//区间比较
                    if (compared.compareTo(new BigDecimal(rule.getMin().toString())) >= 0
                            && compared.compareTo(new BigDecimal(rule.getMax().toString())) < 0) {
                        factor[0] = rule.getFactor();
                        break;
                    }
                } else if (rule.getComparisonSymbol() == 2) {//等值==比较
                    if (compared.compareTo(new BigDecimal(rule.getMin().toString())) == 0) {
                        factor[0] = rule.getFactor();
                        break;
                    }
                } else if (rule.getComparisonSymbol() == 3) {//<=比较
                    if (compared.compareTo(new BigDecimal(rule.getMin().toString())) <= 0) {
                        factor[0] = rule.getFactor();
                        break;
                    }
                } else if (rule.getComparisonSymbol() == 4) {//>=比较
                    if (compared.compareTo(new BigDecimal(rule.getMin().toString())) >= 0) {
                        factor[0] = rule.getFactor();
                        break;
                    }
                }
            }
        });
        return factor[0];
    }
}
