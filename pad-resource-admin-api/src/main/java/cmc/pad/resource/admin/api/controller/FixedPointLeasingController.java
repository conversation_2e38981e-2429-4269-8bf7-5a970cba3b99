package cmc.pad.resource.admin.api.controller;

import cmc.pad.resource.application.query.CinemaLevelQueryService;
import cmc.pad.resource.application.query.FixedPointLeasingPriceQueryService;
import cmc.pad.resource.application.query.data.CinemaLevelInfo;
import cmc.pad.resource.application.query.data.FixedPointPriceQuery;
import cmc.pad.resource.domain.cinema.CinemaRepository;
import cmc.pad.resource.domain.dictionary.DictionaryDomainService;
import cmc.pad.resource.domain.price.FixedPointLeasingPrice;
import cmc.pad.resource.admin.api.model.price.FixedPointLeasingPriceModel;
import cmc.pad.resource.admin.api.util.MoneyUtils;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.data.PageResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

/**
 * 固定点位刊例报价
 *
 * <AUTHOR>
 * @Date 2019/4/1 11:04
 * @Version 1.0
 */
@Slf4j
@RestController
@RequestMapping("fixed-point-leasing/price")
public class FixedPointLeasingController extends BaseLeasingController {

    private final FixedPointLeasingPriceQueryService queryService;

    @Autowired
    FixedPointLeasingController(FixedPointLeasingPriceQueryService queryService,
                                CinemaLevelQueryService cinemaLevelQueryService,
                                CinemaRepository cinemaRepository,
                                DictionaryDomainService dictionaryDomainService) {
        super(cinemaLevelQueryService, cinemaRepository, dictionaryDomainService);
        this.queryService = queryService;
    }


    @RequestMapping(value = "query", method = {GET})
    public List queryByGet(@ModelAttribute @Validated FixedPointLeasingPriceModel.QueryParams params) {
        return getList(params);
    }

    @RequestMapping(value = "query", method = {POST})
    public List queryByPost(@RequestBody @Validated FixedPointLeasingPriceModel.QueryParams params) {
        return getList(params);
    }

    private List getList(FixedPointLeasingPriceModel.QueryParams params) {
        log.info(">>>查询固定点位刊例价, {}", params);
        String cinemaCode = params.getCinemaCode();
        String cityDistrictLevel = params.getCityLevel();
        String cinemaLevel = params.getCinemaLevel();
        if (StringUtils.isNotBlank(cinemaCode)) {
            CinemaLevelInfo cinemaLevelInfo = getCinemaLevel(cinemaCode);
            cinemaLevel = cinemaLevelInfo.getCinemaLevel();
            cityDistrictLevel = cinemaLevelInfo.getCityDistrictLevel();
        } else {
            validateCityDistrictLevel(cityDistrictLevel);
            validateCinemaLevel(cinemaLevel);
        }
        FixedPointPriceQuery query = new FixedPointPriceQuery();
        query.setCityLevel(cityDistrictLevel);
        query.setCinemaLevel(cinemaLevel);
        PageResult<FixedPointLeasingPrice> pageResult = queryService.effectivePage(query);
        List<FixedPointLeasingPrice> items = pageResult.getItems();
        int totalCount = pageResult.getTotalCount();
        if (totalCount > 0) {
            return items.stream().map(o -> {
                FixedPointLeasingPriceModel.Info info = new FixedPointLeasingPriceModel.Info();
                info.setCityLevel(o.getCityLevel());
                info.setCinemaLevel(o.getCinemaLevel());
                info.setBasePrice(MoneyUtils.centConvertYuan(o.getUnitPrice()));
                return info;
            }).collect(Collectors.toList());
        }
        return Collections.EMPTY_LIST;
    }

}
