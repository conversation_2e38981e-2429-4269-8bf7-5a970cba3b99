package cmc.pad.resource.admin.api.controller;

import cmc.pad.resource.admin.api.model.price.LightBoxPriceModel;
import cmc.pad.resource.admin.api.util.MoneyUtils;
import cmc.pad.resource.application.query.*;
import cmc.pad.resource.application.query.data.AdvertisingPointPriceQuery;
import cmc.pad.resource.application.query.data.CinemaLevelInfo;
import cmc.pad.resource.domain.cinema.CinemaRepository;
import cmc.pad.resource.domain.dictionary.DictionaryDomainService;
import cmc.pad.resource.domain.price.LightBoxPrice;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.data.PageResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

/**
 * 灯箱报价
 */
@Slf4j
@RestController
@RequestMapping("light-box/price")
public class LightBoxPriceController extends BaseLeasingController {

    private final LightBoxQueryService queryService;

    @Autowired
    LightBoxPriceController(LightBoxQueryService queryService,
                            CinemaLevelQueryService cinemaLevelQueryService,
                            CinemaRepository cinemaRepository,
                            DictionaryDomainService dictionaryDomainService) {
        super(cinemaLevelQueryService, cinemaRepository, dictionaryDomainService);
        this.queryService = queryService;
    }


    @RequestMapping(value = "query", method = {GET})
    public List queryByGet(@ModelAttribute @Validated LightBoxPriceModel.QueryParams params) {
        return getList(params);
    }

    @RequestMapping(value = "query", method = {POST})
    public List queryByPost(@RequestBody @Validated LightBoxPriceModel.QueryParams params) {
        return getList(params);
    }

    private List getList(LightBoxPriceModel.QueryParams params) {
        log.info(">>>查询灯箱刊例价, {}", params);
        String cinemaCode = params.getCinemaCode();
        String cityDistrictLevel = params.getCityLevel();
        String cinemaLevel = params.getCinemaLevel();
        if (StringUtils.isNotBlank(cinemaCode)) {
            CinemaLevelInfo cinemaLevelInfo = getCinemaLevel(cinemaCode);
            cinemaLevel = cinemaLevelInfo.getCinemaLevel();
            cityDistrictLevel = cinemaLevelInfo.getCityDistrictLevel();
        } else {
            if (StringUtils.isNotBlank(cityDistrictLevel))
                validateCityDistrictLevel(cityDistrictLevel);
            if (StringUtils.isNotBlank(cinemaLevel))
                validateCinemaLevel(cinemaLevel);
        }
        AdvertisingPointPriceQuery query = new AdvertisingPointPriceQuery();
        query.setCityLevel(cityDistrictLevel);
        query.setCinemaLevel(cinemaLevel);
        PageResult<LightBoxPrice> pageResult = queryService.effectivePage(query);
        List<LightBoxPrice> items = pageResult.getItems();
        int totalCount = pageResult.getTotalCount();
        if (totalCount > 0) {
            return items.stream().map(o -> {
                LightBoxPriceModel.Info info = new LightBoxPriceModel.Info();
                info.setCityLevel(o.getCityLevel());
                info.setCinemaLevel(o.getCinemaLevel());
                info.setBasePrice(MoneyUtils.centConvertYuan(o.getMinTotalPrice()));
                info.setBaseArea(o.getMinArea());
                info.setExtendedPrice(MoneyUtils.centConvertYuan(o.getExpandedUnitPrice()));
                return info;
            }).collect(Collectors.toList());
        }
        return Collections.EMPTY_LIST;
    }

}
