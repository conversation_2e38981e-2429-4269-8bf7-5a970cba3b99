package cmc.pad.resource.admin.api.controller;

import cmc.pad.resource.application.query.CinemaLevelQueryService;
import cmc.pad.resource.application.query.MarketingPointLeasingPriceQueryService;
import cmc.pad.resource.application.query.data.CinemaLevelInfo;
import cmc.pad.resource.application.query.data.MarketingPointQuery;
import cmc.pad.resource.domain.cinema.CinemaRepository;
import cmc.pad.resource.domain.dictionary.DictionaryDomainService;
import cmc.pad.resource.domain.price.MarketingPointLeasingPrice;
import cmc.pad.resource.admin.api.constant.LeaseMethod;
import cmc.pad.resource.admin.api.model.price.MarketingPointLeasingPriceModel;
import cmc.pad.resource.admin.api.util.MoneyUtils;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.data.PageResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

/**
 * 营销点位刊例价
 *
 * <AUTHOR>
 * @Date 2019/4/1 11:04
 * @Version 1.0
 */
@Slf4j
@RestController
@RequestMapping("marketing-point-leasing/price")
public class MarketingPointLeasingController extends BaseLeasingController {

    private final MarketingPointLeasingPriceQueryService queryService;

    @Autowired
    MarketingPointLeasingController(MarketingPointLeasingPriceQueryService queryService,
                                    CinemaLevelQueryService cinemaLevelQueryService,
                                    CinemaRepository cinemaRepository,
                                    DictionaryDomainService dictionaryDomainService) {
        super(cinemaLevelQueryService, cinemaRepository, dictionaryDomainService);
        this.queryService = queryService;
    }

    @RequestMapping(value = "query", method = {GET})
    public List queryByGet(@ModelAttribute @Validated MarketingPointLeasingPriceModel.QueryParams params) {
        return getList(params);
    }

    @RequestMapping(value = "query", method = {POST})
    public List queryByPost(@RequestBody @Validated MarketingPointLeasingPriceModel.QueryParams params) {
        return getList(params);
    }

    private List getList(MarketingPointLeasingPriceModel.QueryParams params) {
        log.info(">>>查询营销点位刊例价, 参数:{}", params.toString());
        String cinemaCode = params.getCinemaCode();
        String cityDistrictLevel = params.getCityLevel();
        String cinemaLevel = params.getCinemaLevel();
        String leaseMethodCode = params.getLeaseMethod();
        if (StringUtils.isNotBlank(cinemaCode)) {
            CinemaLevelInfo cinemaLevelInfo = getCinemaLevel(cinemaCode);
            cinemaLevel = cinemaLevelInfo.getCinemaLevel();
            cityDistrictLevel = cinemaLevelInfo.getCityDistrictLevel();
        } else {
            validateCityDistrictLevel(cityDistrictLevel);
            validateCinemaLevel(cinemaLevel);
            validateLeaseMethod(leaseMethodCode);
        }
        MarketingPointQuery query = new MarketingPointQuery();
        query.setCityLevel(cityDistrictLevel);
        query.setCinemaLevel(cinemaLevel);
        PageResult<MarketingPointLeasingPrice> pageResult = queryService.effectivePage(query);
        List<MarketingPointLeasingPrice> items = pageResult.getItems();
        int totalCount = pageResult.getTotalCount();
        LeaseMethod leaseMethod = LeaseMethod.valueByCode(leaseMethodCode);
        if (totalCount > 0) {
            List<MarketingPointLeasingPriceModel.Info> list = new ArrayList<>();
            items.forEach(o -> {
                MarketingPointLeasingPriceModel.Info info = new MarketingPointLeasingPriceModel.Info();
                info.setCityLevel(o.getCityLevel());
                info.setCinemaLevel(o.getCinemaLevel());
                if (LeaseMethod.AREA.equals(leaseMethod)) {
                    info.setLeaseMethod(LeaseMethod.AREA.getCode());
                    info.setBasePrice(MoneyUtils.centConvertYuan(o.getUnitPriceByArea()));
                } else if (LeaseMethod.QUANTITY.equals(leaseMethod)) {
                    info.setLeaseMethod(LeaseMethod.QUANTITY.getCode());
                    info.setBasePrice(MoneyUtils.centConvertYuan(o.getUnitPriceByQuantity()));
                } else {
                    info.setLeaseMethod(LeaseMethod.AREA.getCode());
                    info.setBasePrice(MoneyUtils.centConvertYuan(o.getUnitPriceByArea()));
                    MarketingPointLeasingPriceModel.Info info2 = new MarketingPointLeasingPriceModel.Info();
                    BeanUtils.copyProperties(info, info2);
                    info2.setLeaseMethod(LeaseMethod.QUANTITY.getCode());
                    info2.setBasePrice(MoneyUtils.centConvertYuan(o.getUnitPriceByQuantity()));
                    list.add(info2);
                }
                list.add(info);
            });
            return list;
        }
        return Collections.EMPTY_LIST;
    }

}
