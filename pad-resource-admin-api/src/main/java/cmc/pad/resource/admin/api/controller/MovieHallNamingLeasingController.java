package cmc.pad.resource.admin.api.controller;

import cmc.pad.resource.admin.api.model.price.MovieHallNamingLeasingPriceModel;
import cmc.pad.resource.admin.api.util.MoneyUtils;
import cmc.pad.resource.application.query.CinemaLevelQueryService;
import cmc.pad.resource.application.query.MovieHallNamingLeasingPriceQueryService;
import cmc.pad.resource.application.query.data.MovieHallNamingQuery;
import cmc.pad.resource.domain.cinema.CinemaRepository;
import cmc.pad.resource.domain.dictionary.DictionaryDomainService;
import cmc.pad.resource.domain.price.MovieHallNamingPrice;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.data.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

/**
 * 冠名厅刊例报价
 *
 * <AUTHOR>
 * @Date 2019/4/1 11:04
 * @Version 1.0
 */
@Slf4j
@RestController
@RequestMapping("named-movie-hall-leasing/price")
public class MovieHallNamingLeasingController extends BaseLeasingController {

    private final MovieHallNamingLeasingPriceQueryService queryService;

    @Autowired
    MovieHallNamingLeasingController(MovieHallNamingLeasingPriceQueryService queryService,
                                     CinemaLevelQueryService cinemaLevelQueryService,
                                     CinemaRepository cinemaRepository,
                                     DictionaryDomainService dictionaryDomainService) {
        super(cinemaLevelQueryService, cinemaRepository, dictionaryDomainService);
        this.queryService = queryService;
    }


    @RequestMapping(value = "query", method = {GET})
    public List queryByGet(@ModelAttribute @Validated MovieHallNamingLeasingPriceModel.QueryParams params) {
        return getList(params);
    }

    @RequestMapping(value = "query", method = {POST})
    public List queryByPost(@RequestBody @Validated MovieHallNamingLeasingPriceModel.QueryParams params) {
        return getList(params);
    }

    private List getList(MovieHallNamingLeasingPriceModel.QueryParams params) {
        log.info(">>>查询冠名厅租赁刊例价, 参数:{}", params.toString());
        String cinemaCode = params.getCinemaCode();
        String movieHallType = params.getMovieHallType();
        validateCinemaCode(cinemaCode);
        MovieHallNamingQuery query = new MovieHallNamingQuery();
        query.setCinemaCode(cinemaCode);
        query.setMovieHallType(movieHallType);
        PageResult<MovieHallNamingPrice> pageResult = queryService.effectivePage(query);
        List<MovieHallNamingPrice> items = pageResult.getItems();
        if (pageResult.getTotalCount() > 0) {
            return items.stream().map(o -> {
                return getInfo(o);
            }).collect(Collectors.toList());
        } else {
            PageResult<MovieHallNamingPrice> result = queryService.effectivePageBack(query);//查询兜底的刊例（未设置影厅类型的刊例）
            if (result.getTotalCount() > 0) {
                return result.getItems().stream().map(o -> {
                    return getInfo(o);
                }).collect(Collectors.toList());
            }
        }
        return Collections.EMPTY_LIST;
    }

    private MovieHallNamingLeasingPriceModel.Info getInfo(MovieHallNamingPrice o) {
        MovieHallNamingLeasingPriceModel.Info info = new MovieHallNamingLeasingPriceModel.Info();
        info.setMovieHallType(o.getMovieHallType());
        info.setBasePrice(MoneyUtils.centConvertYuan(o.getUnitPrice()));
        info.setCinemaCode(o.getCinemaCode());
        return info;
    }

    @RequestMapping(value = "test", method = {POST})
    public String testPost() {
        return "test";
    }

}
