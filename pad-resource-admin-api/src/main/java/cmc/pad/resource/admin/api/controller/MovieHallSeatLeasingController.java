package cmc.pad.resource.admin.api.controller;

import cmc.pad.resource.application.query.CinemaLevelQueryService;
import cmc.pad.resource.application.query.MovieHallSeatLeasingPriceQueryService;
import cmc.pad.resource.application.query.data.CinemaLevelInfo;
import cmc.pad.resource.application.query.data.MovieHallSeatPriceQuery;
import cmc.pad.resource.domain.cinema.CinemaRepository;
import cmc.pad.resource.domain.dictionary.DictionaryDomainService;
import cmc.pad.resource.domain.price.MovieHallSeatLeasingPrice;
import cmc.pad.resource.admin.api.model.price.MovieHallSeatLeasingPriceModel;
import cmc.pad.resource.admin.api.util.MoneyUtils;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.data.PageResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

/**
 * 影厅租赁报价
 *
 * <AUTHOR>
 * @Date 2019/4/1 11:04
 * @Version 1.0
 */
@Slf4j
@RestController
@RequestMapping("movie-hall-seat-leasing/price")
public class MovieHallSeatLeasingController extends BaseLeasingController {
    private final MovieHallSeatLeasingPriceQueryService queryService;

    @Autowired
    MovieHallSeatLeasingController(MovieHallSeatLeasingPriceQueryService queryService,
                                   CinemaLevelQueryService cinemaLevelQueryService,
                                   CinemaRepository cinemaRepository,
                                   DictionaryDomainService dictionaryDomainService) {
        super(cinemaLevelQueryService, cinemaRepository, dictionaryDomainService);
        this.queryService = queryService;
    }


    @RequestMapping(value = "query", method = {GET})
    public List queryByGet(@ModelAttribute @Validated MovieHallSeatLeasingPriceModel.QueryParams params) {
        return getList(params);
    }

    @RequestMapping(value = "query", method = {POST})
    public List queryByPost(@RequestBody @Validated MovieHallSeatLeasingPriceModel.QueryParams params) {
        return getList(params);
    }

    private List getList(MovieHallSeatLeasingPriceModel.QueryParams params) {
        log.info(">>>查询影厅租赁刊例价, {}", params);
        String cinemaCode = params.getCinemaCode();
        String cityDistrictLevel = params.getCityLevel();
        String cinemaLevel = params.getCinemaLevel();
        String movieHallType = params.getMovieHallType();
        if (StringUtils.isNotBlank(cinemaCode)) {
            CinemaLevelInfo cinemaLevelInfo = getCinemaLevel(cinemaCode);
            cinemaLevel = cinemaLevelInfo.getCinemaLevel();
            cityDistrictLevel = cinemaLevelInfo.getCityDistrictLevel();
        } else {
            validateCityDistrictLevel(cityDistrictLevel);
            validateCinemaLevel(cinemaLevel);
        }
        if (StringUtils.isNotBlank(movieHallType)) {
            validateMovieHallType(movieHallType);
        }
        MovieHallSeatPriceQuery query = new MovieHallSeatPriceQuery();
        query.setCityLevel(cityDistrictLevel);
        query.setCinemaLevel(cinemaLevel);
        query.setMovieHallType(movieHallType);
        PageResult<MovieHallSeatLeasingPrice> pageResult = queryService.effectivePage(query);
        List<MovieHallSeatLeasingPrice> items = pageResult.getItems();
        int totalCount = pageResult.getTotalCount();
        if (totalCount > 0) {
            return items.stream().map(o -> {
                return buildHallSeatPriceInfo(o);
            }).collect(Collectors.toList());
        } else {
            //查询兜底的刊例，返回不指定具体影厅的刊例
            PageResult<MovieHallSeatLeasingPrice> result = queryService.effectivePageBack(query);
            if (result.getTotalCount() > 0) {
                return result.getItems().stream().map(o -> {
                    return buildHallSeatPriceInfo(o);
                }).collect(Collectors.toList());
            }
        }
        return Collections.EMPTY_LIST;
    }

    private MovieHallSeatLeasingPriceModel.Info buildHallSeatPriceInfo(MovieHallSeatLeasingPrice o) {
        MovieHallSeatLeasingPriceModel.Info info = new MovieHallSeatLeasingPriceModel.Info();
        info.setCityLevel(o.getCityLevel());
        info.setCinemaLevel(o.getCinemaLevel());
        info.setMovieHallType(o.getMovieHallType());
        info.setBasePrice(MoneyUtils.centConvertYuan(o.getMinTotalPrice()));
        info.setBaseDuration(o.getMinHours());
        info.setExtendedPrice(MoneyUtils.centConvertYuan(o.getExpandedUnitPrice()));
        return info;
    }

}
