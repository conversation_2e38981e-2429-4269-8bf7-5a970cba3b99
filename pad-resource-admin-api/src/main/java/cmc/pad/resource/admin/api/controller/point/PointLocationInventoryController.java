package cmc.pad.resource.admin.api.controller.point;

import cmc.common.utility.copy.CopyUtil;
import cmc.pad.resource.admin.api.model.point.PointLocationParamModel;
import cmc.pad.resource.application.command.point.inventory.occupy.PointInventoryOccupationService;
import cmc.pad.resource.domain.resource.PointLocationModel;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by fuwei on 2022/1/27.
 */
@Slf4j
@RestController
@RequestMapping("point-location/inventory")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PointLocationInventoryController {
    private final PointInventoryOccupationService service;

    @RequestMapping(value = "occupy")
    public boolean occupy(@RequestBody @Validated PointLocationParamModel.InventoryOccupationContractParam param) {
        log.info(">>>提交合同库存占用,param {}", JSON.toJSONString(param));
        service.occupy(CopyUtil.copy(param, PointLocationModel.InventoryOccupationContractParam.class));
        return true;
    }

    @RequestMapping(value = "updateStatus")
    public boolean updateStatus(@RequestBody @Validated PointLocationParamModel.UpdateStatusParam param) {
        log.info(">>>更新库存状态,param:{}", JSON.toJSONString(param));
        PointLocationModel.UpdateStatusParam updateStatusParam = CopyUtil.copy(param, PointLocationModel.UpdateStatusParam.class);
        service.updateStatus(updateStatusParam);
        return true;
    }
}