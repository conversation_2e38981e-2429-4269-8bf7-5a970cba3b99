package cmc.pad.resource.admin.api.controller.point;

import cmc.pad.resource.admin.api.model.PointLocationQueryParam;
import cmc.pad.resource.application.query.point.PointLocationQueryService;
import cmc.pad.resource.domain.resource.PointLocationModel;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.data.PageResult;
import mtime.lark.util.lang.FaultException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by fuwei on 2022/1/27.
 */
@Slf4j
@RestController
@RequestMapping("point-location")
public class PointLocationQueryController {
    @Autowired
    private PointLocationQueryService service;

    @ResponseBody
    @RequestMapping(value = "query")
    public PageResult<PointLocationModel.QueryPointLocationUsableAreaData> query(@RequestBody @Validated PointLocationQueryParam param) {
        log.info(">>>点位查询, param:{}", param);
        PageResult<PointLocationModel.QueryPointLocationUsableAreaData> pageResult
                =
                service.query(
                        param.getPageIndex(), param.getPageSize(),
                        param.getCode(),
                        param.getCinemaInnerCode(), param.getBusinessTypeCode(),
                        param.getStart(), param.getEnd()
                );
        log.info(">>>点位查询, param:{} result:{}", param, JSON.toJSONString(pageResult));
        return pageResult;
    }
}