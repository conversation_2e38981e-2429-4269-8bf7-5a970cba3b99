package cmc.pad.resource.admin.api.model;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@Data
public class PointLocationQueryParam {
    /**
     * 注意框架定义变量命名规则, 前端用_分割的变量会自动转换成驼峰变量
     * 例如:前端page_index会自动转换赋值到pageIndex
     */
    private int pageIndex;
    private int pageSize = 10;
    private String code;
    private String cinemaInnerCode;
    private String businessTypeCode;
    @NotNull
    private LocalDate start;
    @NotNull
    private LocalDate end;
}