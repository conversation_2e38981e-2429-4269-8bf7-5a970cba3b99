package cmc.pad.resource.admin.api.model.point;

import cmc.pad.resource.admin.api.model.validation.BusinessType;
import cmc.pad.resource.admin.api.model.validation.CinemaCode;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * Created by fuwei on 2022/1/12.
 */
@Data
public class PointLocationParamModel {
    @Data
    @NoArgsConstructor
    public static class InventoryOccupationContractParam {
        private int contractType;//1:新提交合同 2:变更已审核过的合同
        @NotNull
        private String contractNo;
        @NotNull
        @BusinessType
        private String businessTypeCode;
        @NotNull
        @Valid
        private List<OccupationDetail> details;

        @Data
        @NoArgsConstructor
        public static class OccupationDetail {
            @NotNull
            @CinemaCode
            private String cinemaInnerCode;
            @NotNull
            private Integer pointLocationId;
            @NotNull
            private Float amount;
            @NotNull
            private LocalDate startDate;
            @NotNull
            private LocalDate endDate;
            @NotNull
            private String id;
            @NotNull
            private Integer status; //1.占用（包含不变的和修改的） 2.新增 3.作废
        }
    }

    @Data
    public static class UpdateStatusParam {
        @NotNull
        private String contractNo;
        @NotNull
        private Integer status;//1:扣减 2:取消
    }
}
