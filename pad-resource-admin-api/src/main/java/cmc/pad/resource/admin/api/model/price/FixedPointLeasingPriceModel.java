package cmc.pad.resource.admin.api.model.price;

import cmc.pad.resource.admin.api.model.validation.CinemaCode;
import cmc.pad.resource.admin.api.model.validation.CinemaLevel;
import cmc.pad.resource.admin.api.model.validation.CityLevel;
import lombok.Data;

/**
 * 固定点位报价
 *
 * <AUTHOR>
 * @Date 2019/4/1 11:06
 * @Version 1.0
 */
public class FixedPointLeasingPriceModel {

    @Data
    public static class QueryParams {
        //城市级别编码
        @CityLevel
        private String cityLevel;
        //影院级别编码
        @CinemaLevel
        private String cinemaLevel;
        //影院编码(内码),当影院编码被赋值时，城市级别和影院级别参数将失效。
        @CinemaCode
        private String cinemaCode;
    }

    @Data
    public static class Info {
        //城市级别编码
        private String cityLevel;
        //影院级别编码
        private String cinemaLevel;
        //基础价:单位：元/平米/月
        private float basePrice;
    }
}
