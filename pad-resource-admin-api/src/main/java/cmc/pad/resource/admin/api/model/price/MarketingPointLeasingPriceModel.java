package cmc.pad.resource.admin.api.model.price;

import cmc.pad.resource.admin.api.model.validation.CinemaCode;
import cmc.pad.resource.admin.api.model.validation.CinemaLevel;
import cmc.pad.resource.admin.api.model.validation.CityLevel;
import cmc.pad.resource.admin.api.model.validation.LeaseMethod;
import lombok.Data;
import lombok.ToString;

/**
 * 营销点位刊例价
 *
 * <AUTHOR>
 * @Date 2019/4/1 11:06
 * @Version 1.0
 */
public class MarketingPointLeasingPriceModel {

    @Data
    @ToString
    public static class QueryParams {
        //城市级别编码
        @CityLevel
        private String cityLevel;
        //影院级别编码
        @CinemaLevel
        private String cinemaLevel;
        //影院编码(内码),当影院编码被赋值时，城市级别和影院级别参数将失效。
        @CinemaCode
        private String cinemaCode;
        //租赁方式
        @LeaseMethod
        private String leaseMethod;
    }

    @Data
    @ToString
    public static class Info {
        //城市级别编码
        private String cityLevel;
        //影院级别编码
        private String cinemaLevel;
        //租赁方式
        private String leaseMethod;
        //基础价:按面积租赁时单位：元/平米/天,按数量租赁时单位：元/个/天
        private float basePrice;
    }
}
