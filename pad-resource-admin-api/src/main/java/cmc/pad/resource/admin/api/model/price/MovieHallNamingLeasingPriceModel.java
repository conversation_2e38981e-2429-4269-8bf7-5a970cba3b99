package cmc.pad.resource.admin.api.model.price;

import cmc.pad.resource.admin.api.model.validation.CinemaCode;
import cmc.pad.resource.admin.api.model.validation.MovieHallType;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * 冠名厅刊例
 *
 * <AUTHOR>
 * @Date 2019/4/1 11:06
 * @Version 1.0
 */
public class MovieHallNamingLeasingPriceModel {

    @Data
    @ToString
    public static class QueryParams {
        //影院编码(内码)
        @NotNull
        @CinemaCode
        private String cinemaCode;
        //影厅类型编码，见cmc维数据"影厅类型"
        @MovieHallType
        private String movieHallType;
    }

    @Data
    @ToString
    public static class Info {
        //影院编码(内码)
        private String cinemaCode;
        //影厅类型编码，见cmc维数据"影厅类型"
        private String movieHallType;
        //基础价:元/年
        private float basePrice;
    }
}
