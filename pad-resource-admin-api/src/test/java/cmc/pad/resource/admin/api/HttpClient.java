//package cmc.pad.resource.admin.api;
//
//import com.google.common.collect.Lists;
//import lombok.extern.slf4j.Slf4j;
//import mtime.lark.util.lang.FaultException;
//import org.apache.http.HttpStatus;
//import org.apache.http.NameValuePair;
//import org.apache.http.client.entity.UrlEncodedFormEntity;
//import org.apache.http.client.methods.*;
//import org.apache.http.entity.StringEntity;
//import org.apache.http.impl.client.CloseableHttpClient;
//import org.apache.http.impl.client.HttpClients;
//import org.apache.http.message.BasicNameValuePair;
//
//import java.io.IOException;
//import java.io.InputStream;
//import java.util.List;
//import java.util.Map;
//
///**
// * Created by fuwei on 2018/6/26.
// */
//@Slf4j
//public class HttpClient {
//    private HttpClient() {
//    }
//
//    public static String postJson(String url, String jsonParam) throws IOException, FaultException {
//        System.out.println(">>> url:" + url + ", param:" + jsonParam);
//        try (CloseableHttpClient client = HttpClients.createDefault()) {
//            HttpPost httpPost = new HttpPost(url);
//            StringEntity entity = new StringEntity(jsonParam, "UTF-8");
//            httpPost.setEntity(entity);
//            httpPost.setHeader("Accept", "application/json");
//            httpPost.setHeader("Content-type", "application/json;charset=UTF-8");
//            try (CloseableHttpResponse response = client.execute(httpPost)) {
//                if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
//                    try (InputStream insContent = response.getEntity().getContent()) {
//                        return inputStream2String(insContent);
//                    }
//                }
//                throw new FaultException(response.getStatusLine().getStatusCode(), "post请求异常");
//            }
//        }
//    }
//
//    public static String post(String url, Map<String, String> params) throws IOException, FaultException {
//        System.out.println(">>>url:" + url);
//        try (CloseableHttpClient client = HttpClients.createDefault()) {
//            HttpPost httpPost = new HttpPost(url);
//            List<NameValuePair> nvps = Lists.newArrayList();
//            for (String name : params.keySet()) {
//                nvps.add(new BasicNameValuePair(name, params.get(name)));
//            }
//            httpPost.addHeader("host", "card-open-api.qas.cmc.com");
//            httpPost.addHeader("pwd", "111111");
//            httpPost.addHeader("sc", "11051401");
//            httpPost.addHeader("x-forwarded-for", "*************");
//            httpPost.addHeader("x-real-ip", "*************");
//            httpPost.addHeader("x-timestamp", "1570672568");
//            httpPost.addHeader("x-verifycode", "d5636a66ec69554d2bd728121c83b55a");
//            httpPost.setEntity(new UrlEncodedFormEntity(nvps));
//            try (CloseableHttpResponse response = client.execute(httpPost)) {
//                if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
//                    try (InputStream insContent = response.getEntity().getContent()) {
//                        return inputStream2String(insContent);
//                    }
//                }
//                throw new FaultException(response.getStatusLine().getStatusCode(), "post请求异常");
//            }
//        }
//    }
//
//    public static String get(String url) throws IOException, FaultException {
//        System.out.println(">>>url:" + url);
//        try (CloseableHttpClient client = HttpClients.createDefault()) {
//            HttpGet httpGet = new HttpGet(url);
//            try (CloseableHttpResponse response = client.execute(httpGet)) {
//                if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
//                    try (InputStream insContent = response.getEntity().getContent()) {
//                        return inputStream2String(insContent);
//                    }
//                }
//                throw new FaultException(response.getStatusLine().getStatusCode(), "get请求异常");
//            }
//        }
//    }
//
//    private static String inputStream2String(InputStream in) throws IOException {
//        StringBuffer out = new StringBuffer();
//        byte[] b = new byte[4096];
//        for (int n; (n = in.read(b)) != -1; ) {
//            out.append(new String(b, 0, n));
//        }
//        return out.toString();
//    }
//}
