package cmc.pad.resource.admin.api;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.lang.FaultException;
import okhttp3.*;

import java.io.IOException;
import java.util.Map;

import static cmc.pad.resource.admin.api.controller.UrlConstant.HOST;

/**
 * Created by fuwei on 2021/12/10.
 */
@Slf4j
public class HttpUtil {
    private HttpUtil() {
    }

    public static String login() {
        return "";
//        Map<String, String> param = Maps.newHashMap();
//        param.put("loginName", "admin1");
//        param.put("password", "123456");
//        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), JSON.toJSONString(param));
//        Request request = new Request.Builder()
//                .url(DOMAIN + "portal/auth/login")
//                .post(requestBody)
//                .build();
//        String token = "";
//        try {
//            Response response = new OkHttpClient().newCall(request).execute();
//            JSONObject jsonObject = JSON.parseObject(response.body().string());
//            JSONObject dataJsonObj = JSON.parseObject(jsonObject.get("data").toString());
//            token = dataJsonObj.get("token").toString();
//            System.out.println(token);
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//        return token;
    }

    public static void get(String path) {
        String url = HOST + path;
        log.info(">>>get url:{}", url);
        Request request = new Request.Builder()
                .headers(Headers.of("Authorization", login()))
                .url(url)
                .get()
                .build();
        try {
            Response response = new OkHttpClient().newCall(request).execute();
            System.out.println(JSON.toJSONString(JSONObject.parse(response.body().string()), true));
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void post(String path, Map<String, String> param) {
        String url = HOST + path;
        FormBody.Builder builder = new FormBody.Builder();
        param.forEach((k, v) -> builder.add(k, v));
        log.info(">>>post url:{}, param:{}", url, JSON.toJSONString(param));
        Request request = new Request.Builder()
                .headers(Headers.of("Authorization", login()))
                .url(url)
                .post(builder.build())
                .build();
        try {
            Response response = new OkHttpClient().newCall(request).execute();
            System.out.println(JSON.toJSONString(JSONObject.parse(response.body().string()), true));
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    static ObjectMapper mapper = new ObjectMapper();

    public static void postJson(String path, Object jsonParam) {
        String json = null;
        try {
            json = mapper.writeValueAsString(jsonParam);
        } catch (JsonProcessingException e) {
            throw new FaultException(e);
        }
        String url = HOST + path;
        System.out.println(">>>post json url:" + url + ", param:" + json);
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), json);
        Request request = new Request.Builder()
                .headers(Headers.of("Authorization", login()))
                .url(url)
                .post(requestBody)
                .build();
        try {
            Response response = new OkHttpClient().newBuilder().build().newCall(request).execute();
            System.out.println(JSON.toJSONString(JSONObject.parse(response.body().string()), true));
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
