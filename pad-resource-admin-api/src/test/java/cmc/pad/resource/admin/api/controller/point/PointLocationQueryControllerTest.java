package cmc.pad.resource.admin.api.controller.point;

import cmc.pad.resource.admin.api.HttpUtil;
import com.google.common.collect.Maps;
import lombok.SneakyThrows;
import org.junit.Test;

import java.util.Map;

/**
 * Created by fuwei on 2022/1/28.
 */
public class PointLocationQueryControllerTest {
    @Test
    @SneakyThrows
    public void query() {

        Map<String, String> p = Maps.newHashMap();
        p.put("page_index", "1");
        p.put("page_size", "10");
//        p.put("cinema_inner_code", "111");
//        p.put("business_type_code", "GD");
        p.put("start", "2022-03-01");
        p.put("end", "2022-06-02");
        p.put("code", "Y110");
        HttpUtil.postJson("point-location/query", p);
    }
}