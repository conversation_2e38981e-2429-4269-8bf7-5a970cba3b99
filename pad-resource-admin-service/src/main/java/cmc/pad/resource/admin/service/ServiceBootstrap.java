/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package cmc.pad.resource.admin.service;

import cmc.pad.resource.admin.service.spring.PadAdminServiceProxyAutoConfig;
import mtime.lark.net.rpc.RpcApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;


@SpringBootApplication(exclude = {PadAdminServiceProxyAutoConfig.class, MongoAutoConfiguration.class})
public class ServiceBootstrap {

    public static void main(String[] args) {
        new RpcApplication(ServiceBootstrap.class, args).run();
    }
}
