package cmc.pad.resource.admin.util;

import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2019/3/19 16:27
 * @Version 1.0
 */
public class MoneyUtils {
    public static int yuanConvertCent(String yuan) {
        if (StringUtils.isBlank(yuan)) {
            return 0;
        }
        BigDecimal multiply = BigDecimal.valueOf(Double.valueOf(yuan)).multiply(BigDecimal.valueOf(100L));
        return multiply.intValue();
    }

    public static String centConvertYuan(int money) {
        if (money <= 0) {
            money = 0 - money;
        }
        java.text.DecimalFormat df = new java.text.DecimalFormat("#.00");
        String yuan = df.format((double) money / 100);
        if (yuan.startsWith(".")) {
            yuan = 0 + yuan;
        }
        return yuan;
    }
}
