package cmc.pad.resource.domain.discount;

import mtime.lark.util.lang.EnumDisplayNameSupport;
import mtime.lark.util.lang.EnumValueSupport;

/**
 * <AUTHOR>
 */
public enum BusinessType implements EnumValueSupport, EnumDisplayNameSupport {
    YT(1, "影厅座位租赁"),
    YX(2, "营销点位租赁"),
    WZ(3, "外租区域租赁"),
    GD(4, "固定点位租赁"),
    XC(5, "宣传点位租赁"),
    GMT(6, "冠名厅租赁");

    private int value;
    private String displayName;

    BusinessType(int value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    @Override
    public String displayName() {
        return displayName;
    }

    @Override
    public int value() {
        return value;
    }
}
